'use client';

import React, { useState } from 'react';
import {
  Paper,
  TableBody,
  TablePagination,
  Chip,
  IconButton,
  Stack,
  Typography,
  Box,
  Tooltip,
  TableSortLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  InputAdornment
} from '@mui/material';
import {
  StyledTableContainer,
  StyledTable,
  StyledTableHead,
  StyledHeaderCell,
  StyledTableRow,
  StyledTableCell
} from '../common/TablePresets';
import VisibilityIcon from '@mui/icons-material/Visibility';
import SearchIcon from '@mui/icons-material/Search';
import { formatDistanceToNow } from 'date-fns';

interface Ticket {
  id: string;
  title: string;
  description?: string;
  status: 'open' | 'in_progress' | 'waiting_on_customer' | 'resolved' | 'closed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
  creator: { id: string; email: string };
  assignee?: { id: string; email: string };
  ticket_targets?: Array<{ target_type: string; target_id: number }>;
}

interface TicketListProps {
  tickets: Ticket[];
  loading?: boolean;
  onTicketClick: (ticket: Ticket) => void;
  onStatusFilter?: (status: string) => void;
  onSearch?: (searchTerm: string) => void;
}

const statusColors = {
  open: 'primary',
  in_progress: 'info',
  waiting_on_customer: 'warning',
  resolved: 'success',
  closed: 'default',
  cancelled: 'error'
} as const;

const priorityColors = {
  low: 'default',
  medium: 'primary',
  high: 'warning',
  urgent: 'error'
} as const;

const statusLabels = {
  open: 'Open',
  in_progress: 'In Progress',
  waiting_on_customer: 'Waiting on Customer',
  resolved: 'Resolved',
  closed: 'Closed',
  cancelled: 'Cancelled'
};

const priorityLabels = {
  low: 'Low',
  medium: 'Medium',
  high: 'High',
  urgent: 'Urgent'
};

export function TicketList({ 
  tickets, 
  loading = false, 
  onTicketClick, 
  onStatusFilter, 
  onSearch 
}: TicketListProps) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [orderBy, setOrderBy] = useState<keyof Ticket>('created_at');
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSort = (property: keyof Ticket) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleStatusFilterChange = (event: any) => {
    const newStatus = event.target.value;
    setStatusFilter(newStatus);
    onStatusFilter?.(newStatus === 'all' ? '' : newStatus);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchTerm = event.target.value;
    setSearchTerm(newSearchTerm);
    onSearch?.(newSearchTerm);
  };

  const filteredAndSortedTickets = React.useMemo(() => {
    let filtered = [...tickets];

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(ticket =>
        ticket.title.toLowerCase().includes(searchLower) ||
        (ticket.description && ticket.description.toLowerCase().includes(searchLower)) ||
        ticket.id.toString().includes(searchLower)
      );
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      const aValue = a[orderBy];
      const bValue = b[orderBy];

      if (aValue < bValue) {
        return order === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return order === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [tickets, orderBy, order, searchTerm]);

  const paginatedTickets = filteredAndSortedTickets.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      {/* Filters */}
      <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <TextField
            size="small"
            placeholder="Search tickets..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 200 }}
          />
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={statusFilter}
              label="Status"
              onChange={handleStatusFilterChange}
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="open">Open</MenuItem>
              <MenuItem value="in_progress">In Progress</MenuItem>
              <MenuItem value="waiting_on_customer">Waiting on Customer</MenuItem>
              <MenuItem value="resolved">Resolved</MenuItem>
              <MenuItem value="closed">Closed</MenuItem>
              <MenuItem value="cancelled">Cancelled</MenuItem>
            </Select>
          </FormControl>
        </Stack>
      </Box>

      <StyledTableContainer>
        <StyledTable aria-label="tickets table">
          <StyledTableHead>
            <StyledTableRow>
              <StyledHeaderCell>
                <TableSortLabel
                  active={orderBy === 'id'}
                  direction={orderBy === 'id' ? order : 'asc'}
                  onClick={() => handleSort('id')}
                >
                  ID
                </TableSortLabel>
              </StyledHeaderCell>
              <StyledHeaderCell>
                <TableSortLabel
                  active={orderBy === 'title'}
                  direction={orderBy === 'title' ? order : 'asc'}
                  onClick={() => handleSort('title')}
                >
                  Title
                </TableSortLabel>
              </StyledHeaderCell>
              <StyledHeaderCell>Status</StyledHeaderCell>
              <StyledHeaderCell>Priority</StyledHeaderCell>
              <StyledHeaderCell>
                <TableSortLabel
                  active={orderBy === 'created_at'}
                  direction={orderBy === 'created_at' ? order : 'asc'}
                  onClick={() => handleSort('created_at')}
                >
                  Created
                </TableSortLabel>
              </StyledHeaderCell>
              <StyledHeaderCell align="right">Actions</StyledHeaderCell>
            </StyledTableRow>
          </StyledTableHead>
          <TableBody>
            {paginatedTickets.map((ticket) => (
              <StyledTableRow 
                key={ticket.id}
                hover
                sx={{ cursor: 'pointer' }}
                onClick={() => onTicketClick(ticket)}
              >
                <StyledTableCell>#{ticket.id}</StyledTableCell>
                <StyledTableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {ticket.title}
                    </Typography>
                    {ticket.description && (
                      <Typography 
                        variant="caption" 
                        color="text.secondary"
                        sx={{ 
                          display: 'block',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: 300
                        }}
                      >
                        {ticket.description}
                      </Typography>
                    )}
                  </Box>
                </StyledTableCell>
                <StyledTableCell>
                  <Chip
                    label={statusLabels[ticket.status]}
                    color={statusColors[ticket.status]}
                    size="small"
                  />
                </StyledTableCell>
                <StyledTableCell>
                  <Chip
                    label={priorityLabels[ticket.priority]}
                    color={priorityColors[ticket.priority]}
                    size="small"
                    variant="outlined"
                  />
                </StyledTableCell>
                <StyledTableCell>
                  <Tooltip title={new Date(ticket.created_at).toLocaleString()}>
                    <Typography variant="body2" color="text.secondary">
                      {formatDistanceToNow(new Date(ticket.created_at), { addSuffix: true })}
                    </Typography>
                  </Tooltip>
                </StyledTableCell>
                <StyledTableCell align="right">
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      onTicketClick(ticket);
                    }}
                  >
                    <VisibilityIcon />
                  </IconButton>
                </StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </StyledTable>
      </StyledTableContainer>
      
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={filteredAndSortedTickets.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
}
